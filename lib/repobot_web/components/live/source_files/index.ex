defmodule RepobotWeb.Live.SourceFiles.Index do
  use RepobotWeb, :live_view

  alias Repobot.{SourceFiles, Tags, Categories}

  def ai do
    Repobot.AI.backend()
  end

  def mount(_params, _session, socket) do
    {:ok,
     assign(socket,
       source_files: get_source_files(socket.assigns),
       selected_tags: [],
       all_tags:
         Tags.list_tags_with_source_files(
           socket.assigns.current_user,
           socket.assigns.current_organization
         ),
       modal_tags:
         Tags.list_tags(socket.assigns.current_user, socket.assigns.current_organization),
       all_categories: Categories.list_categories(socket.assigns.current_organization),
       selected_files: [],
       show_bulk_tag_modal: false,
       modal_selected_tags: [],
       new_tag_name: ""
     )}
  end

  def handle_event("filter_tags", %{"tags" => tags}, socket) do
    selected_tags = tags |> Map.values() |> Enum.reject(&(&1 == ""))
    filtered_files = filter_by_tags(get_source_files(socket.assigns), selected_tags)

    {:noreply, assign(socket, source_files: filtered_files, selected_tags: selected_tags)}
  end

  # Handle the case when all tags are unchecked
  def handle_event("filter_tags", _params, socket) do
    filtered_files = get_source_files(socket.assigns)
    {:noreply, assign(socket, source_files: filtered_files, selected_tags: [])}
  end

  def handle_event("delete", %{"id" => id}, socket) do
    source_file = SourceFiles.get_source_file!(id)
    {:ok, _} = SourceFiles.delete_source_file(source_file)

    {:noreply,
     assign(socket,
       source_files: get_source_files(socket.assigns),
       all_tags:
         Tags.list_tags_with_source_files(
           socket.assigns.current_user,
           socket.assigns.current_organization
         ),
       modal_tags:
         Tags.list_tags(socket.assigns.current_user, socket.assigns.current_organization)
     )}
  end

  def handle_event("bulk_delete", _params, socket) do
    {:ok, _count} = SourceFiles.delete_source_files(socket.assigns.selected_files)

    {:noreply,
     assign(socket,
       source_files: get_source_files(socket.assigns),
       selected_files: [],
       all_tags:
         Tags.list_tags_with_source_files(
           socket.assigns.current_user,
           socket.assigns.current_organization
         ),
       modal_tags:
         Tags.list_tags(socket.assigns.current_user, socket.assigns.current_organization)
     )}
  end

  def handle_event("toggle_file_selection", %{"id" => id}, socket) do
    selected_files = socket.assigns.selected_files

    updated_selected =
      if id in selected_files do
        List.delete(selected_files, id)
      else
        [id | selected_files]
      end

    {:noreply, assign(socket, :selected_files, updated_selected)}
  end

  def handle_event("show_bulk_tag_modal", _, socket) do
    # Get all unique tags from selected files
    current_tags =
      socket.assigns.selected_files
      |> Enum.map(&SourceFiles.get_source_file!(&1))
      |> Enum.flat_map(& &1.tags)
      |> Enum.uniq_by(& &1.id)
      |> Enum.map(& &1.name)
      |> Enum.sort()

    {:noreply,
     assign(socket,
       show_bulk_tag_modal: true,
       modal_selected_tags: current_tags
     )}
  end

  def handle_event("hide_bulk_tag_modal", _, socket) do
    {:noreply,
     assign(socket,
       show_bulk_tag_modal: false,
       modal_selected_tags: []
     )}
  end

  def handle_event("toggle_modal_tag", %{"tag" => tag_name}, socket) do
    updated_selected =
      if tag_name in socket.assigns.modal_selected_tags do
        List.delete(socket.assigns.modal_selected_tags, tag_name)
      else
        [tag_name | socket.assigns.modal_selected_tags]
      end
      |> Enum.sort()

    {:noreply, assign(socket, :modal_selected_tags, updated_selected)}
  end

  def handle_event("save_tags", _params, socket) do
    # Get all tags first to ensure they exist
    tags =
      Tags.get_or_create_tags(socket.assigns.modal_selected_tags, socket.assigns.current_user)

    # Update each file with the selected tags
    Enum.each(socket.assigns.selected_files, fn file_id ->
      source_file = SourceFiles.get_source_file!(file_id)

      SourceFiles.update_source_file(source_file, %{
        "tags" => Enum.map(tags, & &1.name) |> Enum.sort()
      })
    end)

    {:noreply,
     socket
     |> assign(:show_bulk_tag_modal, false)
     |> assign(:selected_files, [])
     |> assign(:modal_selected_tags, [])
     |> assign(:source_files, SourceFiles.list_source_files(socket.assigns.current_user))}
  end

  def handle_event("clear_selection", _, socket) do
    {:noreply, assign(socket, :selected_files, [])}
  end

  def handle_event("create_and_add_tag", %{"name" => name}, socket) when name != "" do
    case Tags.create_tag(%{
           "name" => name,
           "user_id" => socket.assigns.current_user.id,
           "organization_id" => socket.assigns.current_organization.id
         }) do
      {:ok, tag} ->
        # Add the new tag to all selected files
        Enum.each(socket.assigns.selected_files, fn file_id ->
          source_file = SourceFiles.get_source_file!(file_id)
          current_tags = Enum.map(source_file.tags, & &1.name)
          all_tags = [tag.name | current_tags] |> Enum.uniq()

          SourceFiles.update_source_file(source_file, %{"tags" => all_tags})
        end)

        # Update the modal's selected tags to include the new tag
        updated_selected = [tag.name | socket.assigns.modal_selected_tags] |> Enum.uniq()

        {:noreply,
         socket
         |> assign(
           :all_tags,
           Tags.list_tags_with_source_files(
             socket.assigns.current_user,
             socket.assigns.current_organization
           )
         )
         |> assign(
           :modal_tags,
           Tags.list_tags(socket.assigns.current_user, socket.assigns.current_organization)
         )
         |> assign(:source_files, SourceFiles.list_source_files(socket.assigns.current_user))
         |> assign(:modal_selected_tags, updated_selected)
         |> assign(:new_tag_name, "")}

      {:error, _changeset} ->
        {:noreply, socket}
    end
  end

  def handle_event("create_and_add_tag", _params, socket) do
    {:noreply, socket}
  end

  def handle_event("update_new_tag_name", %{"name" => name}, socket) do
    {:noreply, assign(socket, :new_tag_name, name)}
  end

  def handle_event("create_tag", %{"name" => name}, socket) when name != "" do
    case Tags.create_tag(%{
           "name" => name,
           "user_id" => socket.assigns.current_user.id,
           "organization_id" => socket.assigns.current_organization.id
         }) do
      {:ok, tag} ->
        # Add the new tag to the selected tags
        updated_selected = [tag.name | socket.assigns.modal_selected_tags]

        {:noreply,
         socket
         |> assign(
           :all_tags,
           Tags.list_tags_with_source_files(
             socket.assigns.current_user,
             socket.assigns.current_organization
           )
         )
         |> assign(
           :modal_tags,
           Tags.list_tags(socket.assigns.current_user, socket.assigns.current_organization)
         )
         |> assign(:new_tag_name, "")
         |> assign(:modal_selected_tags, updated_selected)}

      {:error, _changeset} ->
        {:noreply, socket}
    end
  end

  def handle_event("create_tag", _params, socket) do
    {:noreply, socket}
  end

  def handle_event("delete_tag", %{"id" => tag_id}, socket) do
    tag = Enum.find(socket.assigns.modal_tags, &(&1.id == tag_id))

    case Tags.delete_tag(tag) do
      {:ok, _} ->
        # Remove the tag from modal_selected_tags if it was selected
        updated_selected = List.delete(socket.assigns.modal_selected_tags, tag.name)

        {:noreply,
         socket
         |> assign(
           :all_tags,
           Tags.list_tags_with_source_files(
             socket.assigns.current_user,
             socket.assigns.current_organization
           )
         )
         |> assign(
           :modal_tags,
           Tags.list_tags(socket.assigns.current_user, socket.assigns.current_organization)
         )
         |> assign(:modal_selected_tags, updated_selected)}

      {:error, _changeset} ->
        {:noreply, socket}
    end
  end

  def handle_event("auto_categorize", _, socket) do
    # Only get uncategorized files
    uncategorized_files = Enum.filter(socket.assigns.source_files, &is_nil(&1.category))

    if uncategorized_files == [] do
      {:noreply, put_flash(socket, :info, "All files are already categorized")}
    else
      socket =
        case ai().infer_categories(uncategorized_files, socket.assigns.current_organization) do
          {:ok, categories_map} ->
            # Create any new categories that don't exist
            categories_map
            |> Map.values()
            |> Enum.uniq()
            |> Enum.each(fn category_name ->
              unless Enum.any?(socket.assigns.all_categories, &(&1.name == category_name)) do
                Categories.create_category(%{
                  name: category_name,
                  organization_id: socket.assigns.current_organization.id
                })
              end
            end)

            # Refresh categories list
            all_categories = Categories.list_categories(socket.assigns.current_organization)

            # Update each uncategorized file with its inferred category
            Enum.each(uncategorized_files, fn file ->
              if category_name = categories_map[file.id] do
                if category = Enum.find(all_categories, &(&1.name == category_name)) do
                  SourceFiles.update_source_file(file, %{category_id: category.id})
                end
              end
            end)

            socket
            |> put_flash(:info, "Uncategorized files have been automatically categorized")
            |> assign(
              source_files:
                SourceFiles.list_source_files(socket.assigns.current_user)
                |> Repobot.Repo.preload(:source_repository),
              all_categories: all_categories
            )

          {:error, reason} ->
            put_flash(socket, :error, "Failed to categorize files: #{reason}")
        end

      {:noreply, socket}
    end
  end

  def render(assigns) do
    ~H"""
    <div class="p-6">
      <div class="flex justify-between items-center mb-8">
        <div>
          <h1 class="text-2xl font-semibold text-slate-900">Source Files</h1>
          <p class="mt-2 text-sm text-slate-600">
            A list of all your source files that can be synced across repositories.
          </p>
        </div>
        <div class="flex gap-4">
          <button
            phx-click="auto_categorize"
            phx-disable-with="Categorizing..."
            class="inline-flex items-center justify-center rounded-md px-4 py-2 text-sm font-medium shadow-sm transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 bg-indigo-100 text-indigo-700 hover:bg-indigo-200"
          >
            <.icon name="hero-sparkles" class="w-4 h-4 mr-2" /> Auto-categorize
          </button>
          <.link
            navigate={~p"/source-files/new"}
            class="inline-flex items-center justify-center rounded-md bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-colors duration-150"
          >
            Add Source File
          </.link>
        </div>
      </div>

      <%= if length(@selected_files) > 0 do %>
        <div class="mb-6 p-4 bg-slate-50 rounded-lg border border-slate-200">
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-4">
              <span class="text-sm font-medium text-slate-700">
                {length(@selected_files)} files selected
              </span>
              <button
                phx-click="show_bulk_tag_modal"
                data-test-manage-tags
                class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-150"
              >
                Manage Tags
              </button>
              <button
                phx-click="bulk_delete"
                data-test-bulk-delete
                data-confirm={"Are you sure you want to delete #{length(@selected_files)} selected source files? This action cannot be undone."}
                class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-150"
              >
                <.icon name="hero-trash" class="w-4 h-4 mr-1" /> Delete Selected
              </button>
            </div>
            <button
              phx-click="clear_selection"
              data-test-clear-selection
              class="text-sm text-slate-500 hover:text-slate-700"
            >
              Clear Selection
            </button>
          </div>
        </div>
      <% end %>

      <%= if @show_bulk_tag_modal do %>
        <div class="fixed inset-0 bg-slate-500/75 flex items-center justify-center z-50">
          <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4">
            <div class="p-6">
              <h3 class="text-lg font-medium text-slate-900 mb-4">Manage Tags</h3>
              <div class="space-y-4">
                <div class="flex flex-wrap gap-2">
                  <%= for tag <- @modal_tags do %>
                    <div class="group relative">
                      <button
                        type="button"
                        phx-click="toggle_modal_tag"
                        phx-value-tag={tag.name}
                        data-test-tag-button={tag.name}
                        data-test-tag-selected={
                          if tag.name in @modal_selected_tags, do: "true", else: "false"
                        }
                        class={[
                          "px-3 py-1.5 rounded-full text-sm font-medium transition-all duration-150",
                          "cursor-pointer shadow-sm",
                          if(tag.name in @modal_selected_tags,
                            do: "text-slate-900 scale-105",
                            else: "text-slate-600 hover:border-slate-300"
                          )
                        ]}
                        style={"background-color: #{if tag.name in @modal_selected_tags, do: tag.color, else: "white"}; border: 1px solid #{if tag.name in @modal_selected_tags, do: tag.color, else: "#e5e7eb"}"}
                      >
                        {tag.name}
                      </button>
                      <button
                        type="button"
                        phx-click="delete_tag"
                        phx-value-id={tag.id}
                        data-test-delete-tag={tag.name}
                        class="absolute -top-1 -right-1 hidden group-hover:flex items-center justify-center w-4 h-4 rounded-full bg-red-500 text-white opacity-90 hover:opacity-100 transition-opacity duration-150"
                        title="Remove tag"
                      >
                        <.icon name="hero-x-mark-mini" class="w-3 h-3" />
                      </button>
                    </div>
                  <% end %>
                  <.form
                    for={%{}}
                    class="contents"
                    phx-change="update_new_tag_name"
                    phx-submit="create_tag"
                  >
                    <input
                      type="text"
                      name="name"
                      value={@new_tag_name}
                      placeholder="New tag..."
                      data-test-new-tag-input
                      class="input input-sm"
                      autocomplete="off"
                    />
                  </.form>
                </div>
              </div>
            </div>
            <div class="bg-slate-50 px-6 py-4 flex justify-end gap-2">
              <button
                phx-click="hide_bulk_tag_modal"
                data-test-cancel-tags
                class="px-4 py-2 bg-white border border-slate-300 rounded-md text-slate-700 hover:bg-slate-50"
              >
                Cancel
              </button>
              <button
                phx-click="save_tags"
                data-test-save-tags
                class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-500"
              >
                Save Changes
              </button>
            </div>
          </div>
        </div>
      <% end %>

      <div class="mb-6">
        <.form :let={_f} for={%{}} phx-change="filter_tags" class="flex gap-2 items-center">
          <div class="flex-1">
            <label class="block text-sm font-medium text-slate-700 mb-1">Filter by Tags</label>
            <div class="flex flex-wrap gap-2">
              <%= for tag <- @all_tags do %>
                <label class="relative inline-flex group">
                  <input
                    type="checkbox"
                    name={"tags[#{tag.name}]"}
                    value={tag.name}
                    checked={tag.name in @selected_tags}
                    class="checkbox checkbox-sm absolute w-full h-full opacity-0 cursor-pointer peer"
                  />
                  <div
                    class={[
                      "px-3 py-1.5 rounded-full text-sm font-medium transition-all duration-150",
                      "cursor-pointer shadow-sm",
                      if(tag.name in @selected_tags,
                        do: "text-slate-900 scale-105",
                        else: "text-slate-600 hover:border-slate-300"
                      )
                    ]}
                    style={"background-color: #{if tag.name in @selected_tags, do: tag.color, else: "white"}; border: 1px solid #{if tag.name in @selected_tags, do: tag.color, else: "#e5e7eb"}"}
                  >
                    {tag.name}
                  </div>
                </label>
              <% end %>
            </div>
          </div>
        </.form>
      </div>

      <div class="space-y-8">
        <%= for {category, files} <- group_by_category(@source_files) do %>
          <div>
            <h2 class="text-lg font-medium text-slate-900 mb-4">
              {if category, do: category.name, else: "Uncategorized"}
            </h2>
            <div class="overflow-hidden rounded-lg border border-slate-200">
              <table class="min-w-full divide-y divide-slate-200">
                <thead>
                  <tr class="bg-slate-50">
                    <th class="w-4 px-6 py-3">
                      <span class="sr-only">Select</span>
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                      Name
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                      Tags
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                      Source Repository
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                      Linked Repositories
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                      Last Modified
                    </th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-slate-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-slate-200">
                  <%= for source_file <- files do %>
                    <tr class="hover:bg-slate-50 transition-colors duration-150">
                      <td class="px-6 py-4 whitespace-nowrap">
                        <input
                          type="checkbox"
                          value={source_file.id}
                          checked={source_file.id in @selected_files}
                          phx-click="toggle_file_selection"
                          phx-value-id={source_file.id}
                          data-test-select-file={source_file.id}
                          class="checkbox checkbox-sm"
                        />
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm">
                        <.link
                          navigate={~p"/source-files/#{source_file.id}"}
                          class="text-indigo-600 hover:text-indigo-900 font-medium"
                        >
                          {source_file.name}
                          <%= if source_file.read_only do %>
                            <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-slate-100 text-slate-800">
                              <.icon name="hero-lock-closed" class="w-3 h-3 mr-1" /> Read-only
                            </span>
                          <% end %>
                        </.link>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm">
                        <div class="flex flex-wrap gap-1">
                          <%= for tag <- Enum.sort_by(source_file.tags, & &1.name) do %>
                            <span
                              class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium text-slate-900"
                              style={"background-color: #{tag.color}"}
                            >
                              {tag.name}
                            </span>
                          <% end %>
                        </div>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm">
                        <%= if source_file.source_repository && !match?(%Ecto.Association.NotLoaded{}, source_file.source_repository) do %>
                          <.link
                            navigate={~p"/repositories/#{source_file.source_repository}"}
                            class="text-indigo-600 hover:text-indigo-900"
                          >
                            {source_file.source_repository.full_name}
                          </.link>
                        <% else %>
                          <span class="text-slate-500">—</span>
                        <% end %>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-600">
                        {length(source_file.repositories)} repositories
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-600">
                        {Calendar.strftime(source_file.updated_at, "%Y-%m-%d %H:%M:%S")}
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-right">
                        <div class="flex justify-end gap-2">
                          <%= unless source_file.read_only do %>
                            <.link
                              navigate={~p"/source-files/#{source_file.id}/edit"}
                              class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-150"
                            >
                              Edit
                            </.link>
                            <button
                              phx-click="delete"
                              phx-value-id={source_file.id}
                              data-confirm="Are you sure you want to delete this source file?"
                              class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-150"
                            >
                              Delete
                            </button>
                          <% else %>
                            <span class="text-xs text-slate-500 italic">Read-only</span>
                          <% end %>
                        </div>
                      </td>
                    </tr>
                  <% end %>
                </tbody>
              </table>
            </div>
          </div>
        <% end %>
      </div>
    </div>
    """
  end

  defp get_source_files(%{current_user: user, current_organization: organization}) do
    SourceFiles.list_source_files(user, organization)
    |> Repobot.Repo.preload(:source_repository)
  end

  defp group_by_category(source_files) do
    source_files
    |> Enum.group_by(& &1.category)
    |> Enum.sort_by(fn
      # Put uncategorized at the end
      {nil, _} -> "zzz"
      {category, _} -> category.name
    end)
  end

  defp filter_by_tags(source_files, []), do: source_files

  defp filter_by_tags(source_files, tag_names) do
    Enum.filter(source_files, fn file ->
      file_tag_names = Enum.map(file.tags, & &1.name)
      Enum.all?(tag_names, &(&1 in file_tag_names))
    end)
  end
end
